package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_refund_ticket")
@ApiModel(value="MnjxRefundTicket对象", description="")
public class MnjxRefundTicket extends Model<MnjxRefundTicket> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "refund_id", type = IdType.ASSIGN_ID)
    private String refundId;

    @ApiModelProperty(value = "退票单号")
    @TableField("refund_no")
    private String refundNo;

    @ApiModelProperty(value = "退票单号")
    @TableField("ticket_no")
    private String ticketNo;

    @ApiModelProperty(value = "票价（不含税）")
    @TableField("collection")
    private BigDecimal collection;

    @ApiModelProperty(value = "CN基建费价格")
    @TableField("cn_price")
    private BigDecimal cnPrice;

    @ApiModelProperty(value = "YQ基建费价格")
    @TableField("yq_price")
    private BigDecimal yqPrice;

    @ApiModelProperty(value = "退票代理费")
    @TableField("comm")
    private BigDecimal comm;

    @ApiModelProperty(value = "实际退款金额")
    @TableField("net_refund")
    private BigDecimal netRefund;

    @ApiModelProperty(value = "退款时间")
    @TableField("refund_date")
    private String refundDate;

    @Override
    protected Serializable pkVal() {
        return this.refundId;
    }

}
