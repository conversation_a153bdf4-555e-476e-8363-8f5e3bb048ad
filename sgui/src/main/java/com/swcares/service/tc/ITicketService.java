package com.swcares.service.tc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrNmTicket;
import com.swcares.entity.MnjxPnrSeg;
import com.swcares.obj.dto.QueryTicketByCertDto;
import com.swcares.obj.dto.QueryTicketByDetrDto;
import com.swcares.obj.dto.QueryTicketByPnrDto;
import com.swcares.obj.dto.QueryTicketDetailDto;
import com.swcares.obj.vo.QueryTicketByDetrVo;
import com.swcares.obj.vo.QueryTicketByPnrVo;
import com.swcares.obj.vo.QueryTicketDetailVo;
import com.swcares.obj.vo.TicketByRtktVo;

import java.util.List;

/**
 * 客票服务接口
 *
 * <AUTHOR>
 * @date 2025/5/30 14:00
 */
public interface ITicketService {

    /**
     * 按票号查询客票详情
     *
     * @param dto 查询参数
     * @return 客票详情列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketDetailVo> queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException;

    /**
     * 通过DETR查询票面信息
     *
     * @param dto 查询参数
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException;

    /**
     * 通过RTKT查询票面信息
     *
     * @param ticketNo 票号
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException;

    /**
     * 按PNR查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException;

    /**
     * 按证件号查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketByCert(QueryTicketByCertDto dto) throws SguiResultException;

    /**
     * 构建openSourceText
     *
     * @param ticketNo
     * @param passengerName
     * @param passengerType
     * @param xnBirthDate
     * @param firstSeg
     * @param lastSeg
     * @param issuedTime
     * @param pnrNmTicket
     * @param pnr
     * @return
     */
    String buildOpenSourceText(String ticketNo, String passengerName, String passengerType, String xnBirthDate,
                               MnjxPnrSeg firstSeg, MnjxPnrSeg lastSeg, String issuedTime, MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr);
}
